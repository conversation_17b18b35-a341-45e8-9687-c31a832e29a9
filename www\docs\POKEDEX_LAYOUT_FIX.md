# Pokédex Layout-Verbesserung

## Problem-Beschreibung

**Ursprüngliches Problem:** <PERSON><PERSON>ngen <PERSON>-<PERSON>n (z.B. "Turtok", "Bisaflor", "Glurak") überlappte der `.pokedex-card-name` die absolut positionierte `.pokedex-card-dex` (<PERSON><PERSON><PERSON><PERSON><PERSON>).

Dies führte zu unleserlichen Karten und schlechter UX.

## Lösung: Flexbox-Layout

**Einführung eines neuen Containers** `.pokedex-card-header` mit Flexbox.

### CSS-Eigenschaften:
- `display: flex` - Flexbox-Layout
- `justify-content: space-between` - Name links, Dex-Nummer rechts
- `align-items: center` - Vertikale Zentrierung
- `gap: 8px` - Abstand zwischen Name und Nummer

## Name-Element (.pokedex-card-name)

### CSS-Eigenschaften:
- `flex: 1 1 auto` - Nimmt verfügbaren Platz ein, kann schrumpfen
- `min-width: 0` - Erlaubt Schrumpfen unter Inhaltsgröße
- `overflow: hidden` - Verhindert Überlauf
- `white-space: nowrap` - <PERSON><PERSON>bruch
- **Dynamische Schriftgrößen-Anpassung via JavaScript** (siehe unten)

## Dex-Nummer-Element (.pokedex-card-dex)

### CSS-Eigenschaften:
- `flex: 0 0 auto` - Feste Größe, schrumpft/wächst nicht
- `width: auto` - Automatische Breite basierend auf Inhalt
- `min-width: 24px` - Mindestbreite für 3-stellige Nummern
- `text-align: right` - Rechtsbündige Ausrichtung
- **Nicht mehr absolut positioniert**

## Dynamische Schriftgrößen-Anpassung

### JavaScript-Methode: `adjustCardNameFontSize()`

**Zweck:** Verhindert, dass lange Pokémon-Namen abgeschnitten werden, indem die Schriftgröße dynamisch reduziert wird.

### Funktionsweise:
- Wird nach dem Rendern via `requestAnimationFrame()` aufgerufen (für korrekte DOM-Maße)
- Iteriert über alle `.pokedex-card-name` Elemente
- Startet mit 1rem (16px) Schriftgröße
- Prüft mit `scrollWidth > clientWidth`, ob Text überläuft
- Reduziert Schriftgröße schrittweise um 0.05rem (0.8px)
- Stoppt bei Mindestgröße von 0.65rem (10.4px)

### Mindestschriftgröße: 0.65rem (10.4px)
- Gewährleistet Lesbarkeit auch bei sehr langen Namen
- Verhindert zu kleine, unleserliche Schrift
- Selbst längste deutsche Pokémon-Namen bleiben lesbar

**Code-Referenz:** `www/ui/PokedexScreen.js`, Zeilen 189-218

## HTML-Struktur

```html
<div class="pokedex-card">
  <div class="pokedex-card-header">
    <div class="pokedex-card-name" data-name="Bisaflor">Bisaflor</div>
    <div class="pokedex-card-dex">003</div>
  </div>
  <div class="pokedex-card-types">
    <span class="type-label type-grass">Pflanze</span>
    <span class="type-label type-poison">Gift</span>
  </div>
  <img class="pokedex-card-img" src="..." alt="Bisaflor" />
</div>
```

## Verwendete CSS-Variablen

Die Lösung nutzt bestehende CSS-Variablen aus:
- `www/styles/variables-gui.css` - Farben, Abstände, Schriftgrößen
- `www/styles/type-colors.css` - Typ-spezifische Farben (z.B. `--type-grass`, `--type-fire`)

**Keine neuen CSS-Variablen wurden hinzugefügt.**

## Vorteile der Lösung

✅ **Kein Überlappen** mehr zwischen Name und Dex-Nummer  
✅ **Responsive** - funktioniert auf allen Bildschirmgrößen  
✅ **Lesbar** - selbst längste Namen bleiben lesbar (min. 0.65rem)  
✅ **Performant** - `requestAnimationFrame()` für optimale DOM-Messungen  
✅ **Wartbar** - klare Trennung von CSS (Layout) und JS (dynamische Anpassung)

## Dateien

- **CSS:** `www/styles/pokedex.css` (Zeilen 103-169)
- **JavaScript:** `www/ui/PokedexScreen.js` (Zeilen 170-173, 189-218)
- **Dokumentation:** `www/docs/POKEDEX_LAYOUT_FIX.md` (diese Datei)
