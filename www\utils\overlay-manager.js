// utils/overlay-manager.js
// Centralized overlay management to eliminate code duplication

import { logger } from './logger.js';
import { registerBackButtonHandler } from '../capacitor/app.js';

/**
 * Overlay Manager - Handles creation and management of full-screen overlays
 */
export class OverlayManager {
  /**
   * Create or get an existing overlay element
   * @param {string} id - Unique ID for the overlay
   * @param {string} className - CSS class name for the overlay
   * @returns {HTMLElement} - The overlay element
   */
  static createOverlay(id, className) {
    let overlay = document.getElementById(id);
    
    if (!overlay) {
      overlay = document.createElement('div');
      overlay.id = id;
      overlay.className = className;
      overlay.style.position = 'fixed';
      overlay.style.top = '0';
      overlay.style.left = '0';
      overlay.style.width = '100vw';
      overlay.style.height = '100vh';
      overlay.style.background = 'var(--standard-background-color, #f4f4f4)';
      overlay.style.zIndex = '10020';
      overlay.style.overflowY = 'auto';
      document.body.appendChild(overlay);
      
      logger.debug(`Created overlay with ID: ${id}`);
    }
    
    return overlay;
  }

  /**
   * Hide FAB buttons when overlay opens
   * @returns {Promise<void>}
   */
  static async hideFabButtons() {
    try {
      const { fabManager } = await import('../ui/FabManager.js');
      fabManager.hideAllButtons();
    } catch (e) {
      logger.warn('Could not import fabManager:', e);
    }
  }

  /**
   * Show FAB buttons when overlay closes
   * @returns {Promise<void>}
   */
  static async showFabButtons() {
    try {
      const { fabManager } = await import('../ui/FabManager.js');
      fabManager.showAllButtons();
    } catch (e) {
      logger.warn('Could not import fabManager:', e);
    }
  }

  /**
   * Setup complete overlay with back button handling and FAB management
   * @param {string} id - Unique ID for the overlay
   * @param {string} className - CSS class name for the overlay
   * @param {string} eventName - Event name to dispatch when closing
   * @param {Function} onClose - Optional callback when overlay closes
   * @param {Function} cleanupCallback - Optional cleanup callback for component cleanup (e.g., destroy() method)
   * @returns {Object} - Object with overlay element, close function, and setCleanupCallback method
   */
  static async setupOverlay(id, className, eventName, onClose = null, cleanupCallback = null) {
    // Hide FAB buttons
    await this.hideFabButtons();

    // Create overlay
    const overlay = this.createOverlay(id, className);
    overlay.style.display = 'block';

    let removeBackButtonHandler = null;
    let isClosing = false; // Prevent recursive calls
    let cleanup = cleanupCallback; // Store cleanup callback

    // Create unified close function
    const closeOverlay = async () => {
      // Prevent recursive calls
      if (isClosing) return;
      isClosing = true;

      // Call cleanup callback if provided
      if (cleanup && typeof cleanup === 'function') {
        try {
          await cleanup();
        } catch (e) {
          logger.error('Error in overlay cleanup callback:', e);
        }
      }

      // Remove the back button event listener if it exists
      if (removeBackButtonHandler) {
        removeBackButtonHandler();
        removeBackButtonHandler = null;
      }

      overlay.style.display = 'none';

      // Show FAB buttons when overlay is closed
      await this.showFabButtons();

      // Call optional callback
      if (onClose) {
        onClose();
      }

      // Reset closing flag
      isClosing = false;
    };

    // Setup back button handler
    removeBackButtonHandler = registerBackButtonHandler(closeOverlay);

    // Setup close event listener - use the same close function
    overlay.addEventListener(eventName, closeOverlay);

    return {
      overlay,
      close: closeOverlay,
      removeBackButtonHandler,
      /**
       * Set or update the cleanup callback after overlay creation
       * @param {Function} callback - The cleanup callback function
       */
      setCleanupCallback: (callback) => {
        cleanup = callback;
      }
    };
  }

  /**
   * Close submenu if it's open (helper for battle screens)
   * @returns {Promise<void>}
   */
  static async closeSubmenuIfOpen() {
    try {
      const { fabSubmenuManager } = await import('../ui/FabSubmenuManager.js');
      if (fabSubmenuManager.isActive) {
        fabSubmenuManager.closeSubmenu();
      }
    } catch (e) {
      logger.warn('Could not import fabSubmenuManager:', e);
    }
  }

  /**
   * Hide specific screens (helper for battle screens)
   * @param {string[]} screenIds - Array of screen IDs to hide
   */
  static hideScreens(screenIds) {
    screenIds.forEach(screenId => {
      const screen = document.getElementById(screenId);
      if (screen) screen.style.display = 'none';
    });
  }
}
