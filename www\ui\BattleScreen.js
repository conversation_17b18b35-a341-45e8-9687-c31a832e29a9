// ui/BattleScreen.js
// Battle screen for Pokemon battles

import { Component } from './Component.js';
import { logger } from '../utils/logger.js';
import { registerBackButtonHandler } from '../capacitor/app.js';
import { calculateBattleOutcome, getBattleResultClass } from '../services/battle-calc.js';
import { pokemonManager } from '../services/pokemon-manager.js';
import { getExpForLevel, getExpCurveForRarity } from '../services/experience-system.js';
import { gameState } from '../state/game-state.js';
import { getGermanPokemonName } from '../utils/pokemon-display-names.js';
import { calculateExpProgress, animateExpBar, showLevelUpNotification } from '../utils/battle-utils.js';
import { getTypeIconHtml } from '../utils/pokemon-utils.js';



/**
 * Log battle information to console in a way that works in both browser and Capacitor
 * @param {string} message - The message to log
 */
function logToBattleConsole(message) {
  // Check if we're in a Capacitor environment
  if (window.Capacitor && window.Capacitor.isNativePlatform && window.Capacitor.isNativePlatform()) {
    // In Capacitor, use console.warn for better visibility in logcat
    console.warn('POKEMON_BATTLE_LOG: ' + message);
  } else {
    // In browser, use logger.debug
    logger.debug(message);
  }
}

/**
 * Wendet den Flug-Offset an ein Pokémon-Image an und kombiniert ihn ggf. mit Spiegelung.
 * @param {string} pokemonName - Der Name des Pokémon (z. B. "Zubat").
 * @param {HTMLElement} imgElement - Das Bild-Element, das verschoben werden soll.
 * @param {boolean} isPlayer - Ob es sich um das eigene Pokémon handelt (spiegeln mit scaleX).
 */
function applyFlyingOffset(pokemonName, imgElement, isPlayer = false) {
  // Pokémon-Namen in Kleinbuchstaben umwandeln für case-insensitive Suche
  const pokemonNameLower = pokemonName.toLowerCase();

  // Offset aus dem Pokedex holen
  let offset = 0;

  // Pokedex-Daten aus dem gameState oder window-Objekt holen
  const pokedexData = window.pokedexData || gameState.pokedexData;
  if (pokedexData && pokedexData.length) {
    // Pokémon in den Pokedex-Daten suchen
    const pokemon = pokedexData.find(p =>
      p.name.toLowerCase() === pokemonNameLower ||
      (p.de && p.de.toLowerCase() === pokemonNameLower)
    );

    // Wenn das Pokémon gefunden wurde und einen flying_offset hat, diesen verwenden
    if (pokemon && typeof pokemon.flying_offset === 'number') {
      offset = pokemon.flying_offset;
    }
  }

  // Basis-Transformation (Spiegelung für Spieler-Pokémon)
  const baseTransform = isPlayer ? 'scaleX(-1)' : '';

  // Transformation anwenden
  imgElement.style.transform = `${baseTransform} translateY(${offset}px)`;
}



/**
 * Battle Screen class
 */
export class BattleScreen extends Component {
  constructor(container, options = {}) {
    super(container, options);

    // Store the callback function
    this.onBattleComplete = options.onBattleComplete || null;

    // Animation and timeout tracking for cleanup
    this.activeTimeouts = new Set();
    this.activeAnimations = [];

    // Use provided Pokemon data or fallback to placeholders
    this.playerPokemon = options.playerPokemon || {
      name: 'Pikachu',
      level: 5,
      types: ['electric'], // Kleinbuchstaben für Konsistenz mit pokedex-151.json
      image: './src/PokemonSprites/25.png'
    };

    // Use a different wild Pokemon based on screen orientation if not provided
    const isLandscape = window.innerWidth > window.innerHeight;
    this.wildPokemon = options.wildPokemon || {
      name: isLandscape ? 'Charizard' : 'Bulbasaur',
      level: isLandscape ? 36 : 3,
      types: isLandscape ? ['fire', 'flying'] : ['grass', 'poison'], // Kleinbuchstaben für Konsistenz mit pokedex-151.json
      image: isLandscape ? './src/PokemonSprites/6.png' : './src/PokemonSprites/1.png'
    };

    // Listen for orientation changes to update the display
    this.boundOrientationHandler = this.handleOrientationChange.bind(this);
    window.addEventListener('resize', this.boundOrientationHandler);

    // Initialize player Pokemon experience if not set
    if (this.playerPokemon && typeof this.playerPokemon.level === 'number' && typeof this.playerPokemon.experience !== 'number') {
      const curve = getExpCurveForRarity(this.playerPokemon.rarity || 'common');
      this.playerPokemon.experience = getExpForLevel(this.playerPokemon.level, curve);
      logger.debug(`Initialized ${this.playerPokemon.name}'s experience to ${this.playerPokemon.experience} based on level ${this.playerPokemon.level}`);
    }

    // Calculate battle outcome
    this.battleResult = calculateBattleOutcome(this.playerPokemon, this.wildPokemon);

    // Log detailed battle information to console
    this.logBattleDetails();

    this.render();
    this.addEventListeners();
  }

  /**
   * Handle orientation changes
   */
  handleOrientationChange() {
    // Only update if we're using the default Pokemon
    if (!this.options.wildPokemon) {
      const isLandscape = window.innerWidth > window.innerHeight;

      // Update wild Pokemon based on new orientation
      this.wildPokemon = {
        name: isLandscape ? 'Charizard' : 'Bulbasaur',
        level: isLandscape ? 36 : 3,
        types: isLandscape ? ['fire', 'flying'] : ['grass', 'poison'], // Kleinbuchstaben für Konsistenz mit pokedex-151.json
        image: isLandscape ? './src/PokemonSprites/6.png' : './src/PokemonSprites/1.png'
      };

      // Re-render the screen
      this.render();
      this.addEventListeners();
    }
  }

  /**
   * Create a managed timeout that will be automatically cleared on destroy
   * @param {Function} callback - The callback function to execute
   * @param {number} delay - The delay in milliseconds
   * @returns {number} - The timeout ID
   */
  createManagedTimeout(callback, delay) {
    const timeoutId = setTimeout(() => {
      this.activeTimeouts.delete(timeoutId);
      callback();
    }, delay);
    this.activeTimeouts.add(timeoutId);
    return timeoutId;
  }

  /**
   * Create a managed animation frame that will be automatically cancelled on destroy
   * @param {Function} callback - The callback function to execute
   * @returns {number} - The animation frame ID
   */
  createManagedAnimation(callback) {
    const animationId = requestAnimationFrame(() => {
      const index = this.activeAnimations.indexOf(animationId);
      if (index > -1) {
        this.activeAnimations.splice(index, 1);
      }
      callback();
    });
    this.activeAnimations.push(animationId);
    return animationId;
  }

  /**
   * Render the battle screen
   * @returns {HTMLElement} - The rendered container
   */
  render() {
    try {
      // Use the pre-calculated battle result
      const resultClass = getBattleResultClass(this.battleResult.playerWins, this.battleResult.wasTie);

      // Render the header and battle arena
      this.container.innerHTML = `
        <div class="screen-header battle-header">
    <button class="back-btn" id="battle-back-btn" aria-label="Zurück">
      <img src="./icons/materialicons/chevronleft.svg" alt="Zurück" class="icon-svg" width="38" height="38" />
    </button>
    <h1>Pokémon Battle</h1>
    <div class="header-right"></div>
  </div>

  <div class="battle-container">

    <div class="battle-arena">

        <!-- Info cards container oben -->
        <div class="battle-info-container">
          <div class="pokemon-battle-card player-card">
            <div class="pokemon-battle-header">
              <div class="pokemon-battle-name">${getGermanPokemonName(this.playerPokemon)}</div>
              <div class="pokemon-battle-level">Lvl. ${this.playerPokemon.level || '?'}</div>
            </div>
            <div class="pokemon-health-container">
              <div class="pokemon-health-bar">
                <div class="pokemon-health-fill player-health-fill" style="width: 100%"></div>
              </div>
              <div class="pokemon-health-text">100%</div>
            </div>
            <div class="pokemon-battle-types">${this.renderTypeBadges(this.playerPokemon, true)}</div>
            ${this.renderExpBar(this.playerPokemon)}
          </div>

          <div class="pokemon-battle-card wild-card">
            <div class="pokemon-battle-header">
              <div class="pokemon-battle-name">${getGermanPokemonName(this.wildPokemon)}</div>
              <div class="pokemon-battle-level">Lvl. ${this.wildPokemon.level || '?'}</div>
            </div>
            <div class="pokemon-health-container">
              <div class="pokemon-health-bar">
                <div class="pokemon-health-fill wild-health-fill" style="width: 100%"></div>
              </div>
              <div class="pokemon-health-text">100%</div>
            </div>
            <div class="pokemon-battle-types">${this.renderTypeBadges(this.wildPokemon, false)}</div>
          </div>
        </div>

      <!-- Container mit Background-Image und beiden Seiten -->
      <div class="battle-background" style="background-image: url('./src/battleBackgrounds/bb_forest.png');">

        <!-- Pokemon Images -->
        <div class="wild-side ${this.battleResult.playerWins ? 'wild-defeated' : ''}">
          <img src="${this.wildPokemon.image}" alt="${getGermanPokemonName(this.wildPokemon)}" class="pokemon-battle-image wild-pokemon" />
        </div>
        <div class="player-side ${!this.battleResult.playerWins ? 'player-defeated' : ''}">
          <img src="${this.playerPokemon.image}" alt="${getGermanPokemonName(this.playerPokemon)}" class="pokemon-battle-image player-pokemon" />
        </div>
      </div> <!-- Ende .battle-background -->
    </div> <!-- Ende .battle-arena -->

    <div class="battle-results">
      <span class="${resultClass}">${this.battleResult.resultMessage}</span>
      <div class="battle-calculation">
        <div class="player-level">${this.battleResult.playerDamage || this.playerPokemon.level}</div>
        <div class="battle-calculation-vs">VS</div>
        <div class="wild-level">${this.battleResult.wildDamage || this.wildPokemon.level}</div>
      </div>
      ${this.battleResult.playerWins && this.battleResult.experienceGained ?
        `<div class="battle-exp-gain">
          <span class="exp-gain-text">+${this.battleResult.experienceGained} EXP</span>
        </div>` : ''}
    </div>

    <!-- Continue button as FAB -->
    <button class="fab-button continue-fab" id="battle-continue-btn">
      <img src="./icons/materialicons/close.svg" alt="Schließen" class="icon-svg white-icon" width="24" height="24" />
    </button>
  </div>

      `;

      // Store references to elements
      this.elements = {
        backButton: this.container.querySelector('#battle-back-btn'),
        continueButton: this.container.querySelector('#battle-continue-btn'),
        wildPokemonImage: this.container.querySelector('.wild-pokemon'),
        playerPokemonImage: this.container.querySelector('.player-pokemon')
      };

      // Anwendung der Flug-Offsets auf die Pokémon-Bilder
      if (this.elements.wildPokemonImage) {
        applyFlyingOffset(this.wildPokemon.name, this.elements.wildPokemonImage);
      }

      if (this.elements.playerPokemonImage) {
        applyFlyingOffset(this.playerPokemon.name, this.elements.playerPokemonImage, true);
      }

      this.isRendered = true;
      return this.container;
    } catch (e) {
      logger.error('Error rendering battle screen:', e);
      this.container.innerHTML = `
        <div class="screen-header battle-header">
          <button class="back-btn" id="battle-back-btn" aria-label="Zurück">
            <img src="./icons/materialicons/chevronleft.svg" alt="Zurück" class="icon-svg" width="38" height="38" />
          </button>
          <h1>Pokémon Battle</h1>
          <div class="header-right"></div>
        </div>
        <div class="battle-container">
          <div class="battle-arena">
            <img src="./src/battleBackgrounds/bb_forest.png" alt="Forest Battle Background" class="battle-background">

            <!-- Info cards container at the top -->
            <div class="battle-info-container">
              <div class="pokemon-battle-card player-card">
                <div class="error-message">
                  <p>Fehler beim Laden des Battle-Screens.</p>
                </div>
              </div>
            </div>

            <div class="player-side">
              <!-- Error state - no Pokemon image -->
            </div>
          </div>
          <div class="battle-results">
            <span class="battle-defeat">Battle calculation error!</span>
            <div class="battle-calculation">
              <div class="player-level">?</div>
              <div class="battle-calculation-vs">VS</div>
              <div class="wild-level">?</div>
            </div>
          </div>
          <!-- Continue button as FAB -->
          <button class="fab-button continue-fab" id="battle-continue-btn">
            <img src="./icons/materialicons/close.svg" alt="Schließen" class="icon-svg white-icon" width="24" height="24" />
          </button>
        </div>
      `;

      this.elements = {
        backButton: this.container.querySelector('#battle-back-btn'),
        continueButton: this.container.querySelector('#battle-continue-btn'),
        wildPokemonImage: this.container.querySelector('.wild-pokemon'),
        playerPokemonImage: this.container.querySelector('.player-pokemon')
      };

      return this.container;
    }
  }



  /**
   * Render type badges with effectiveness multipliers
   * @param {Object} pokemon - The Pokemon data
   * @param {boolean} isPlayer - Whether this is the player's Pokemon
   * @returns {string} - HTML for the type badges
   */
  renderTypeBadges(pokemon, isPlayer) {
    // Ensure pokemon.types is an array
    const types = Array.isArray(pokemon.types) ? pokemon.types : ['Normal'];

    // Generate type badges with effectiveness multipliers
    const typeBadges = types.map(type => {
      // Get the type effectiveness multiplier if battle result is available
      let effectivenessInfo = '';

      if (this.battleResult && this.battleResult.typeEffectiveness) {
        // For player Pokemon (attacker), we need to check how effective each of its types is against each of the wild Pokemon's types
        // For wild Pokemon (defender), we need to check how effective each of its types is against each of the player Pokemon's types

        // Normalize type to handle case sensitivity
        const normalizedType = type.charAt(0).toUpperCase() + type.slice(1).toLowerCase();

        if (isPlayer) {
          // Player Pokemon (attacker) - show how effective this type is against each defender type
          const attackerData = this.battleResult.typeEffectiveness.attackerToDefender.find(
            data => data.attackerType.toLowerCase() === normalizedType.toLowerCase()
          );

          if (attackerData && attackerData.defenderTypes.length > 0) {
            // Show effectiveness against each defender type
            const effectivenessValues = attackerData.defenderTypes.map(defData => {
              const defenderType = defData.defenderType;
              const multiplier = defData.multiplier;

              let effectivenessClass = 'type-neutral';
              if (multiplier > 1.0) {
                effectivenessClass = 'type-super';
              } else if (multiplier < 1.0 && multiplier >= 0.5) {
                effectivenessClass = 'type-not-very';
              } else if (multiplier < 0.5) {
                effectivenessClass = 'type-no-effect';
              }

              return `<span class="type-effectiveness ${effectivenessClass}" title="${normalizedType} vs ${defenderType}">×${multiplier.toFixed(2)}</span>`;
            });

            effectivenessInfo = effectivenessValues.join('');
          }
        } else {
          // Wild Pokemon (defender) - show how effective this type is against each player type
          const attackerData = this.battleResult.typeEffectiveness.defenderToAttacker.find(
            data => data.attackerType.toLowerCase() === normalizedType.toLowerCase()
          );

          if (attackerData && attackerData.defenderTypes.length > 0) {
            // Calculate individual effectiveness against each player type
            const effectivenessValues = attackerData.defenderTypes.map(defData => {
              const playerType = defData.defenderType;
              const multiplier = defData.multiplier;

              let effectivenessClass = 'type-neutral';
              if (multiplier > 1.0) {
                effectivenessClass = 'type-super';
              } else if (multiplier < 1.0 && multiplier >= 0.5) {
                effectivenessClass = 'type-not-very';
              } else if (multiplier < 0.5) {
                effectivenessClass = 'type-no-effect';
              }

              return `<span class="type-effectiveness ${effectivenessClass}" title="${normalizedType} vs ${playerType}">×${multiplier.toFixed(2)}</span>`;
            });

            effectivenessInfo = effectivenessValues.join('');
          }
        }
      }

      return `
        <div class='pokemon-type-badge-group'>
          <span class='pokemon-type-badge'>${getTypeIconHtml(type)}</span>
          ${effectivenessInfo}
        </div>
      `;
    }).join('');

    return typeBadges;
  }

  /**
   * Render the experience bar for a Pokemon
   * @param {Object} pokemon - The Pokemon data
   * @returns {string} - HTML for the experience bar
   */
  renderExpBar(pokemon) {
    try {
      // Only show exp bar for player's Pokemon
      if (!pokemon) {
        return '';
      }

      // Calculate experience progress
      let expProgress = calculateExpProgress(pokemon);

      // If player won, calculate progress with additional experience
      let newExpWidth = 0;
      let levelUpNotification = '';

      if (this.battleResult && this.battleResult.playerWins && this.battleResult.experienceGained) {
        // Calculate progress with the new experience
        const newExpProgress = calculateExpProgress(pokemon, this.battleResult.experienceGained);
        newExpWidth = newExpProgress.newExpPercentage - expProgress.progressPercentage;

        // Add level up notification if Pokemon will level up
        if (newExpProgress.willLevelUp) {
          levelUpNotification = '<div class="level-up-notification">Level up!</div>';
        }
      }

      return `
        <div class="pokemon-exp-container" style="position: relative;">
          ${levelUpNotification}
          <div class="pokemon-exp-bar">
            <div class="pokemon-exp-fill" style="width: ${expProgress.progressPercentage}%"></div>
            ${newExpWidth > 0 ? `<div class="pokemon-exp-new" style="width: ${newExpWidth}%; right: ${100 - expProgress.progressPercentage - newExpWidth}%;"></div>` : ''}
          </div>
          <div class="pokemon-exp-text">${expProgress.expInCurrentLevel}/${expProgress.expNeededForNextLevel} XP</div>
        </div>
      `;
    } catch (e) {
      logger.error('Error rendering experience bar:', e);
      return '';
    }
  }

  /**
   * Render Pokemon battle info (legacy method, kept for error handling)
   * @param {Object} pokemon - The Pokemon data
   * @param {boolean} isPlayer - Whether this is the player's Pokemon
   * @returns {string} - HTML for the Pokemon battle info
   */
  renderPokemonBattleInfo(pokemon, isPlayer) {
    // Just return the image for the Pokemon
    return `
      <img src="${pokemon.image}" alt="${getGermanPokemonName(pokemon)}" class="pokemon-battle-image ${isPlayer ? 'player-pokemon' : 'wild-pokemon'}" />
    `;
  }

  /**
   * Add event listeners
   */
  addEventListeners() {
    if (this.elements.backButton) {
      this.addEventListener(this.elements.backButton, 'click', () => {
        this.handleBattleComplete();
      });
    }

    if (this.elements.continueButton) {
      this.addEventListener(this.elements.continueButton, 'click', () => {
        this.handleBattleComplete();
      });
    }

    // Set up health bar animations
    this.setupHealthBarAnimations();
  }

  /**
   * Set up health bar animations
   * This adds event listeners to update the health text and add low-health class during animation
   */
  setupHealthBarAnimations() {
    try {
      // Get health bar elements
      const playerHealthFill = this.container.querySelector('.player-health-fill');
      const wildHealthFill = this.container.querySelector('.wild-health-fill');
      const playerHealthText = this.container.querySelector('.player-card .pokemon-health-text');
      const wildHealthText = this.container.querySelector('.wild-card .pokemon-health-text');

      if (!playerHealthFill || !wildHealthFill || !playerHealthText || !wildHealthText) {
        return;
      }

      // Determine which Pokemon is defeated
      const playerDefeated = !this.battleResult.playerWins;
      const wildDefeated = this.battleResult.playerWins;

      // Trigger animations after a short delay
      this.createManagedTimeout(() => {
        // Apply animations directly
        if (playerDefeated) {
          playerHealthFill.style.width = '5%';
          playerHealthFill.classList.add('low-health');
          playerHealthText.textContent = '5%';
        } else {
          playerHealthFill.style.width = '90%';
          playerHealthText.textContent = '90%';
        }

        if (wildDefeated) {
          wildHealthFill.style.width = '5%';
          wildHealthFill.classList.add('low-health');
          wildHealthText.textContent = '5%';
        } else {
          wildHealthFill.style.width = '90%';
          wildHealthText.textContent = '90%';
        }

        // Trigger XP animation if player won
        if (this.battleResult.playerWins && this.battleResult.experienceGained && this.playerPokemon) {
          // Wait for health animations to complete before showing XP gain
          this.createManagedTimeout(() => {
            this.animateExperienceGain();
          }, 700);
        }
      }, 1000); // Start health animations after 1 second
    } catch (e) {
      logger.error('Error setting up health bar animations:', e);
    }
  }

  /**
   * Animate experience gain and show level up notification
   * This is called automatically after battle animations
   */
  async animateExperienceGain() {
    try {
      // Only proceed if player won and gained experience
      if (!this.battleResult.playerWins || !this.battleResult.experienceGained || !this.playerPokemon) {
        return;
      }

      // Check if this is a test battle (test-pikachu-1)
      const isTestBattle = this.playerPokemon.id === 'test-pikachu-1';

      if (isTestBattle) {
        // For test battles, just animate the XP bar without saving data
        logger.debug('Test battle: Animating XP gain without saving data');

        // Calculate experience progress for the test Pokemon
        const expProgress = calculateExpProgress(this.playerPokemon);

        // Animate the experience bar with the simulated XP gain
        animateExpBar(this.container, expProgress, this.battleResult.experienceGained);

        // Always show level up notification for test battles
        logger.debug(`Test battle: Simulating level up for ${this.playerPokemon.name}`);

        // Create a level up notification if it doesn't exist
        let levelUpNotification = this.container.querySelector('.level-up-notification');

        if (!levelUpNotification) {
          // If no notification element exists, create one and add it to the DOM
          const expContainer = this.container.querySelector('.pokemon-exp-container');
          if (expContainer) {
            levelUpNotification = document.createElement('div');
            levelUpNotification.className = 'level-up-notification';
            levelUpNotification.textContent = 'Level up!';
            levelUpNotification.style.opacity = '0'; // Start hidden
            expContainer.appendChild(levelUpNotification);
          }
        }

        // Show the notification with animation
        if (levelUpNotification) {
          // Reset the notification first (in case it was already shown)
          levelUpNotification.style.opacity = '0';

          // Force a reflow to ensure the animation plays again
          void levelUpNotification.offsetWidth;

          // Show the notification
          this.createManagedTimeout(() => {
            levelUpNotification.style.opacity = '1';
          }, 100);

          // Randomly decide whether to show evolution or just level up for test mode
          const simulateEvolution = Math.random() > 0.5;

          // Show level up alert with minimal delay
          this.createManagedTimeout(() => {
            if (simulateEvolution) {
              // Simulate evolution notification with German names
              const currentDisplayName = getGermanPokemonName(this.playerPokemon);
              const evolvedName = this.playerPokemon.name === 'Pikachu' ? 'Raichu' : 'Evolved ' + this.playerPokemon.name;
              const evolvedDisplayName = getGermanPokemonName({ name: evolvedName, dex_number: this.playerPokemon.dex_number });
              alert(`[TEST MODE] Wow! Dein ${currentDisplayName} ist auf Level ${this.playerPokemon.level + 1} aufgestiegen und hat sich zu ${evolvedDisplayName} entwickelt!`);
            } else {
              // Show regular level up notification with German name
              const displayName = getGermanPokemonName(this.playerPokemon);
              alert(`[TEST MODE] Glückwunsch! Dein ${displayName} ist auf Level ${this.playerPokemon.level + 1} aufgestiegen!`);
            }
          }, 500);
        }

        return;
      }

      // For real battles, proceed with saving data
      // Initialize the Pokemon manager
      await pokemonManager.initialize();

      // Get the latest version of the Pokemon
      const pokemon = pokemonManager.getPokemonById(this.playerPokemon.id);

      if (pokemon) {
        // Store the old level and name before updating
        const oldLevel = pokemon.level || 1;
        const oldName = pokemon.name; // Store original name to check for evolution

        // Calculate experience progress before adding new experience
        const expProgress = calculateExpProgress(pokemon);

        // Add the experience to the Pokemon using the proper method
        // This ensures the experience is added consistently and the level is updated properly
        // IMPORTANT: This is the ONLY place where XP should be added to the Pokemon
        // EncountersScreen.js will only show level up alerts but not add XP again
        const expResult = await pokemon.addExperience(this.battleResult.experienceGained);

        // Get the updated level from the result
        const newLevel = expResult.newLevel;

        // Check if the Pokemon has evolved after leveling up
        let hasEvolved = false;
        let evolutionResult = { hasEvolved: false };

        if (newLevel > oldLevel) {
          // Check for evolution and update Pokemon data if evolved
          evolutionResult = await this.checkAndHandleEvolution(pokemon, oldName, oldLevel, newLevel);
          hasEvolved = evolutionResult.hasEvolved;

          if (hasEvolved) {
            logger.debug(`Pokemon evolved: ${oldName} -> ${pokemon.name} at level ${newLevel}`);
            logToBattleConsole(`EVOLUTION: ${oldName} evolved into ${pokemon.name}!`);
          }
        }

        // The Pokemon has already been updated in central storage by the addExperience method
        logToBattleConsole(`Pokemon ${pokemon.name} updated in central storage with level ${pokemon.level} and XP ${pokemon.experience}`);

        // Animate the experience bar
        animateExpBar(this.container, expProgress, this.battleResult.experienceGained);

        // Check if Pokemon leveled up and show notification
        if (newLevel > oldLevel) {
          logger.debug(`${pokemon.name} leveled up from ${oldLevel} to ${newLevel}!`);

          // Show level up notification
          const levelUpNotification = this.container.querySelector('.level-up-notification');
          if (levelUpNotification) {
            levelUpNotification.style.opacity = '1';

            // Show level up alert with minimal delay
            this.createManagedTimeout(() => {
              if (hasEvolved) {
                // Show evolution notification with German names
                const oldDisplayName = getGermanPokemonName({ name: oldName, dex_number: pokemon.dex_number });
                const newDisplayName = getGermanPokemonName(pokemon);
                alert(`Wow! Dein ${oldDisplayName} ist auf Level ${newLevel} aufgestiegen und hat sich zu ${newDisplayName} entwickelt!`);
              } else {
                // Show regular level up notification with German name
                const displayName = getGermanPokemonName(pokemon);
                alert(`Glückwunsch! Dein ${displayName} ist auf Level ${pokemon.level} aufgestiegen!`);
              }
            }, 500);
          } else {
            // Fallback if notification element not found
            this.createManagedTimeout(() => {
              if (hasEvolved) {
                // Show evolution notification with German names
                const oldDisplayName = getGermanPokemonName({ name: oldName, dex_number: pokemon.dex_number });
                const newDisplayName = getGermanPokemonName(pokemon);
                alert(`Wow! Dein ${oldDisplayName} ist auf Level ${newLevel} aufgestiegen und hat sich zu ${newDisplayName} entwickelt!`);
              } else {
                // Show regular level up notification with German name
                const displayName = getGermanPokemonName(pokemon);
                alert(`Glückwunsch! Dein ${displayName} ist auf Level ${pokemon.level} aufgestiegen!`);
              }
            }, 500);
          }
        }
      }
    } catch (e) {
      logger.error('Error animating experience gain:', e);
    }
  }

  /**
   * Check if a Pokemon has evolved after leveling up and handle the evolution
   * @param {Object} pokemon - The player's Pokemon
   * @param {string} oldName - The Pokemon's name before leveling up
   * @param {number} oldLevel - The Pokemon's level before gaining XP
   * @param {number} newLevel - The Pokemon's level after gaining XP
   * @returns {Object} - Result with evolution information
   */
  async checkAndHandleEvolution(pokemon, oldName, oldLevel, newLevel) {
    try {
      // Default result
      const result = {
        hasEvolved: false,
        oldForm: {
          name: oldName,
          level: oldLevel
        },
        newForm: {
          name: pokemon.name,
          level: newLevel
        }
      };

      // Get the pokedex data
      const pokedexData = window.pokedexData || gameState.pokedexData;
      if (!pokedexData || !pokedexData.length) {
        logger.error('Pokedex data not available for evolution check');
        return result;
      }

      // Ensure base_name is set
      if (!pokemon.base_name) {
        pokemon.base_name = pokemon.name.toLowerCase();
        logger.debug(`Setting missing base_name to current name: ${pokemon.base_name}`);
      }

      // Find the Pokemon in the pokedex
      const baseEntry = pokedexData.find(p =>
        p.name.toLowerCase() === pokemon.base_name.toLowerCase() ||
        (p.de && p.de.toLowerCase() === pokemon.base_name.toLowerCase())
      );

      if (!baseEntry) {
        // Try to find by dex_number as fallback
        const dexEntry = pokemon.dex_number ? pokedexData.find(p => p.dex_number === pokemon.dex_number) : null;

        if (dexEntry) {
          logger.debug(`Found entry by dex_number ${pokemon.dex_number} instead of base_name: ${dexEntry.name}`);
          // Update the base_name for future lookups
          pokemon.base_name = dexEntry.name;
        } else {
          logger.error(`Could not find Pokemon in pokedex: base_name=${pokemon.base_name}, dex_number=${pokemon.dex_number}`);
          return result;
        }
      } else {
        // Update Pokemon with correct data from pokedex
        if (!pokemon.dex_number) {
          pokemon.dex_number = baseEntry.dex_number;
          logger.debug(`Updated missing dex_number to ${pokemon.dex_number} from pokedex`);
        }

        if (!pokemon.evolution_chain_id) {
          pokemon.evolution_chain_id = baseEntry.evolution_chain_id;
          logger.debug(`Updated missing evolution_chain_id to ${pokemon.evolution_chain_id} from pokedex`);
        }
      }

      // Get the current display form based on the new level
      // Make sure to await the getDisplayForm call since it's asynchronous
      const displayForm = await pokemon.getDisplayForm(pokedexData);

      // Check if the Pokemon has evolved (name changed)
      if (displayForm.name.toLowerCase() !== oldName.toLowerCase()) {
        logger.debug(`Evolution detected: ${oldName} -> ${displayForm.name} at level ${newLevel}`);

        // Update the Pokemon with the evolved form data
        pokemon.name = displayForm.name;
        pokemon.image_url = displayForm.sprite;
        pokemon.image = displayForm.sprite; // Also update the image property
        pokemon.dex_number = displayForm.dex_number;
        pokemon.types = displayForm.types;
        pokemon.evolution_chain_id = displayForm.evolution_chain_id;

        // Set the result
        result.hasEvolved = true;
        result.newForm.name = displayForm.name;

        // Log the evolution details
        logger.debug(`Pokemon evolved: ${oldName} -> ${displayForm.name}`);
        logger.debug(`New data: dex=${displayForm.dex_number}, types=${displayForm.types.join('/')}`);

        // Update the Pokedex with the evolved Pokemon
        await this.updatePokedexWithEvolvedPokemon(pokemon, displayForm);
      }

      return result;
    } catch (e) {
      logger.error('Error checking for evolution:', e);
      return {
        hasEvolved: false,
        oldForm: { name: oldName, level: oldLevel },
        newForm: { name: pokemon.name, level: newLevel }
      };
    }
  }

  /**
   * Update the Pokedex with the evolved Pokemon
   * @param {Object} pokemon - The evolved Pokemon
   * @param {Object} displayForm - The display form data
   * @returns {Promise<void>}
   */
  async updatePokedexWithEvolvedPokemon(pokemon, displayForm) {
    try {
      // Check if the evolved Pokemon is already in the Pokedex
      const isInPokedex = gameState.pokedex.some(p => {
        // Check by dex_number (most reliable)
        if (p.dex_number && p.dex_number === displayForm.dex_number) {
          return true;
        }

        // Check by name (case-insensitive)
        if (p.name && displayForm.name &&
            p.name.toLowerCase() === displayForm.name.toLowerCase()) {
          return true;
        }

        return false;
      });

      // If the evolved Pokemon is not in the Pokedex, add it
      if (!isInPokedex) {
        logger.debug(`Adding evolved Pokemon ${displayForm.name} (Dex #${displayForm.dex_number}) to Pokedex`);

        // Create a copy of the Pokemon for the Pokedex
        const pokedexEntry = {
          id: pokemon.id,
          name: displayForm.name,
          dex_number: displayForm.dex_number,
          types: displayForm.types,
          image_url: displayForm.sprite,
          evolution_chain_id: displayForm.evolution_chain_id,
          caughtAt: Date.now()
        };

        // Add to the Pokedex
        await gameState.addToPokedex(pokedexEntry);
        logger.debug(`Successfully added ${displayForm.name} to Pokedex`);
      } else {
        logger.debug(`Evolved Pokemon ${displayForm.name} (Dex #${displayForm.dex_number}) is already in Pokedex`);
      }
    } catch (e) {
      logger.error('Error updating Pokedex with evolved Pokemon:', e);
    }
  }

  /**
   * Handle battle completion
   * Call the callback function and close the overlay
   */
  async handleBattleComplete() {
    // Log battle completion
    const resultMessage = `Battle completed: ${this.battleResult.playerWins ? 'Player won' : 'Player lost'} against ${this.wildPokemon.name}`;
    logToBattleConsole(resultMessage);

    // Note: XP animation and level-up notification are now handled automatically in animateExperienceGain()

    // Call the callback function if provided
    if (this.onBattleComplete) {
      this.onBattleComplete(this.battleResult);
    }

    // Close overlay immediately or with minimal delay
    this.createManagedTimeout(() => {
      const overlay = this.container.closest('#battle-overlay');
      if (overlay) {
        overlay.style.display = 'none';
        overlay.dispatchEvent(new Event('closeBattle'));
      }
    }, 100); // Reduced to 100ms for a smoother transition
  }



  // Static flag to track if we've shown the formula explanation
  static hasShownFormulaExplanation = false;

  /**
   * Log detailed battle information to the console
   * Shows both Pokemon data, type effectiveness, battle formula and result
   * Uses logToBattleConsole to ensure visibility in Android logcat
   */
  logBattleDetails() {
    try {
      // Create a battle log header
      logToBattleConsole('🔥 BATTLE START 🔥');

      // Show formula explanation only once per app session
      if (!BattleScreen.hasShownFormulaExplanation) {
        logToBattleConsole('');
        logToBattleConsole('BATTLE FORMULA EXPLANATION:');
        logToBattleConsole('1. Base damage = Pokemon\'s level');
        logToBattleConsole('2. For each attacking type:');
        logToBattleConsole('   a. Calculate effectiveness against each defending type');
        logToBattleConsole('      - Super effective: ×1.25');
        logToBattleConsole('      - Neutral: ×1.0');
        logToBattleConsole('      - Not very effective: ×0.8');
        logToBattleConsole('      - No effect: ×0.2');
        logToBattleConsole('   b. Multiply all type effectiveness values for this attacking type');
        logToBattleConsole('3. Multiply all attacking type multipliers together for total type effectiveness');
        logToBattleConsole('4. Final damage = Level × Total type effectiveness');
        logToBattleConsole('5. Pokemon with higher damage wins (random winner if tied)');
        logToBattleConsole('');

        // Set the flag so we don't show this again
        BattleScreen.hasShownFormulaExplanation = true;
      }

      // Log a concise battle summary with all essential information
      logToBattleConsole(`BATTLE: ${this.playerPokemon.name} (Lvl ${this.playerPokemon.level}, ${Array.isArray(this.playerPokemon.types) ? this.playerPokemon.types.join('/') : 'Normal'}) vs ${this.wildPokemon.name} (Lvl ${this.wildPokemon.level}, ${Array.isArray(this.wildPokemon.types) ? this.wildPokemon.types.join('/') : 'Normal'})`);

      // Player damage calculation
      logToBattleConsole(`Player damage: ${this.playerPokemon.level} (level) × ${this.battleResult.playerTypeMultiplier} (type effectiveness) = ${this.battleResult.playerDamage}`);

      // Type effectiveness breakdown for player
      if (this.battleResult.typeEffectiveness && this.battleResult.typeEffectiveness.attackerToDefender) {
        let typeDetails = [];
        this.battleResult.typeEffectiveness.attackerToDefender.forEach(attackerData => {
          attackerData.defenderTypes.forEach(defenderData => {
            typeDetails.push(`${attackerData.attackerType} vs ${defenderData.defenderType}: ×${defenderData.multiplier.toFixed(2)}`);
          });
        });
        logToBattleConsole(`Player type effectiveness: ${typeDetails.join(', ')}`);
      }

      // Wild Pokemon damage calculation
      logToBattleConsole(`Wild damage: ${this.wildPokemon.level} (level) × ${this.battleResult.wildTypeMultiplier} (type effectiveness) = ${this.battleResult.wildDamage}`);

      // Type effectiveness breakdown for wild Pokemon
      if (this.battleResult.typeEffectiveness && this.battleResult.typeEffectiveness.defenderToAttacker) {
        let typeDetails = [];
        this.battleResult.typeEffectiveness.defenderToAttacker.forEach(attackerData => {
          attackerData.defenderTypes.forEach(defenderData => {
            typeDetails.push(`${attackerData.attackerType} vs ${defenderData.defenderType}: ×${defenderData.multiplier.toFixed(2)}`);
          });
        });
        logToBattleConsole(`Wild type effectiveness: ${typeDetails.join(', ')}`);
      }

      // Log the battle result
      logToBattleConsole(`RESULT: ${this.battleResult.playerWins ? 'PLAYER WINS' : 'PLAYER LOSES'} (${this.battleResult.playerDamage} vs ${this.battleResult.wildDamage})`);
      if (this.battleResult.wasTie) {
        logToBattleConsole('This battle was a tie and was decided randomly!');
      }

      // Log experience gain if player won
      if (this.battleResult.playerWins && this.battleResult.experienceGained) {
        logToBattleConsole(`EXPERIENCE: Player gained ${this.battleResult.experienceGained} EXP points`);
      }

    } catch (error) {
      // Log errors
      console.error('POKEMON_BATTLE_LOG: Error logging battle details:', error);
      logger.error('Error logging battle details:', error);
    }
  }

  /**
   * Clean up resources when the component is destroyed
   * This is called when the battle screen is closed
   */
  destroy() {
    // Clear all active timeouts
    this.activeTimeouts.forEach(timeoutId => {
      clearTimeout(timeoutId);
    });
    this.activeTimeouts.clear();

    // Cancel all active animations
    this.activeAnimations.forEach(animationId => {
      cancelAnimationFrame(animationId);
    });
    this.activeAnimations = [];

    // Remove CSS animation classes from elements
    if (this.container) {
      const healthBars = this.container.querySelectorAll('.health-bar-animation');
      healthBars.forEach(bar => bar.classList.remove('health-bar-animation'));

      const expBars = this.container.querySelectorAll('.exp-bar-animation');
      expBars.forEach(bar => bar.classList.remove('exp-bar-animation'));
    }

    // Remove the orientation change listener using the bound handler
    window.removeEventListener('resize', this.boundOrientationHandler);

    // Call the parent destroy method
    super.destroy && super.destroy();
  }
}

/**
 * Open the battle screen with custom Pokemon
 * @param {Object} playerPokemon - The player's Pokemon
 * @param {Object} wildPokemon - The wild Pokemon
 * @param {Function} callback - Callback function to call when battle is complete
 */
export function openBattleScreenWithCallback(playerPokemon, wildPokemon, callback) {
  // Import fabManager dynamically to avoid circular dependencies
  import('../ui/FabManager.js').then(({ fabManager }) => {
    // Hide all FAB buttons to prevent errors
    fabManager.hideAllButtons();
  }).catch(e => {
    logger.warn('Could not import fabManager:', e);
  });

  let overlay = document.getElementById('battle-overlay');

  if (!overlay) {
    overlay = document.createElement('div');
    overlay.id = 'battle-overlay';
    overlay.className = 'battle-overlay';
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100vw';
    overlay.style.height = '100vh';
    overlay.style.background = 'var(--standard-background-color, #f4f4f4)';
    overlay.style.zIndex = '10020';
    overlay.style.overflowY = 'auto';
    document.body.appendChild(overlay);
  }

  overlay.style.display = 'block';

  // Create the battle screen with the provided Pokemon
  const battleScreen = new BattleScreen(overlay, {
    playerPokemon,
    wildPokemon,
    onBattleComplete: callback
  });

  // Function to close the overlay
  const closeOverlay = () => {
    // Call the battle complete handler to ensure the callback is executed
    // This ensures the Pokemon is removed from encounters when using the hardware back button
    battleScreen.handleBattleComplete();

    overlay.style.display = 'none';
    overlay.dispatchEvent(new Event('closeBattle'));

    // Show all FAB buttons when overlay is closed
    import('../ui/FabManager.js').then(({ fabManager }) => {
      fabManager.showAllButtons();
    }).catch(e => {
      logger.warn('Could not import fabManager:', e);
    });
  };

  // Capture the smartphone's back button
  const removeBackButtonHandler = registerBackButtonHandler(closeOverlay);

  // Close overlay on back event
  overlay.addEventListener('closeBattle', () => {
    // Clean up resources
    battleScreen.destroy();

    // Remove the back button event listener
    removeBackButtonHandler();
    overlay.style.display = 'none';

    // Show all FAB buttons when overlay is closed
    import('../ui/FabManager.js').then(({ fabManager }) => {
      fabManager.showAllButtons();
    }).catch(e => {
      logger.warn('Could not import fabManager:', e);
    });
  });
}

/**
 * Open the battle screen with placeholder Pokemon for testing
 * This function creates a test battle with simulated XP gain
 */
export function openBattleScreen() {
  // First, reset any existing level-up notification from previous test battles
  const existingOverlay = document.getElementById('battle-overlay');
  if (existingOverlay) {
    const existingNotification = existingOverlay.querySelector('.level-up-notification');
    if (existingNotification) {
      // Reset the notification to ensure it can be shown again
      existingNotification.style.opacity = '0';

      // Try to remove it completely to ensure a fresh start
      try {
        existingNotification.parentNode.removeChild(existingNotification);
      } catch (e) {
        // Ignore errors if it can't be removed
      }
    }
  }

  // Create a custom callback that doesn't actually save any data
  const testCallback = (battleResult) => {
    logger.debug('Test battle completed with result:', battleResult);
    // No actual data is saved in this test callback
  };

  // Use the new function with default Pokemon and simulated XP gain
  openBattleScreenWithCallback(
    {
      id: 'test-pikachu-1',
      name: 'Pikachu',
      level: 5,
      types: ['electric'], // Kleinbuchstaben für Konsistenz mit pokedex-151.json
      image: './src/PokemonSprites/25.png',
      rarity: 'starter',
      experience: 125 // Etwas Erfahrung für die Anzeige der XP-Leiste
    },
    {
      id: 'test-opponent-1',
      name: window.innerWidth > window.innerHeight ? 'Charizard' : 'Bulbasaur',
      level: window.innerWidth > window.innerHeight ? 36 : 3,
      types: window.innerWidth > window.innerHeight ? ['fire', 'flying'] : ['grass', 'poison'], // Kleinbuchstaben für Konsistenz mit pokedex-151.json
      image: window.innerWidth > window.innerHeight ? './src/PokemonSprites/6.png' : './src/PokemonSprites/1.png',
      rarity: window.innerWidth > window.innerHeight ? 'rare' : 'starter'
    },
    testCallback
  );
}


