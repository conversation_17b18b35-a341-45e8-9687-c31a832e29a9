@import url('./variables-gui.css');
@import url('./type-colors.css');
@import url('../fonts/poppins.css');
@import url('./battle-screen.css');

html {
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent; /* Deaktiviert den blauen Highlight-Effekt auf mobilen Geräten */
}
*, *:before, *:after {
    box-sizing: inherit;
}

/* Deaktiviere Tap-Highlight für alle Buttons */
button {
    -webkit-tap-highlight-color: transparent;
    touch-callout: none;
    user-select: none;
}

.pokemon-type-badge {
  display: inline-block;
  color: #fff;
  font-size: 0.95em;
  font-weight: bold;
  padding: 0;
  border-radius: 0;
  margin: 0 4px;
  vertical-align: middle;
}

body {
    margin: 0;
    font-family: 'Poppins', Arial, sans-serif;
    background: var(--light-grey);
}

*,
*:before,
*:after {
    font-family: 'Poppins', Arial, sans-serif;
    box-sizing: inherit;
}

h1 {
    text-align: center;
    margin: 16px 0 8px 0;
}

#map {
    width: 100vw;
    height: 100vh;
    margin: 0;
    border-radius: 0;
    box-shadow: none;
    max-width: 100vw;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
}

/* FAB (Floating Action Button) Base Styles */
.fab-bar {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 24px;
    z-index: 10001;
    display: grid;
    grid-template-columns: repeat(4, 1fr); /* Immer 4 gleiche Spalten */
    grid-template-rows: 1fr; /* Nur eine Zeile */
    justify-items: center; /* Zentriert die Buttons in ihren Zellen */
    align-items: center;
    width: 100%;
    max-width: 100%;
    pointer-events: none;
    padding: 0 16px;
    box-sizing: border-box;
}

/* Positionierung der Buttons im Grid */
#submenu-fab {
    grid-column: 4; /* Vierte Spalte */
    grid-row: 1; /* Erste und einzige Zeile */
}

#debug-fab {
    grid-column: 3; /* Dritte Spalte */
    grid-row: 1; /* Erste und einzige Zeile */
}

#center-fab {
    grid-column: 2; /* Zweite Spalte */
    grid-row: 1; /* Erste und einzige Zeile */
}
.fab-btn {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.18);
    border: none;
    outline: none;
    cursor: pointer;
    transition: background-color 0.2s, filter 0.2s;
    margin: 0;
    pointer-events: auto;
    font-size: 32px;
    min-width: 0;
    -webkit-tap-highlight-color: transparent; /* Deaktiviert den blauen Highlight-Effekt auf mobilen Geräten */
    -webkit-touch-callout: none; /* Verhindert das Kontextmenü auf iOS */
    -webkit-user-select: none; /* Verhindert Textauswahl */
    user-select: none; /* Verhindert Textauswahl */
}

#submenu-fab {
    background-color: var(--pinky-red);
    color: #fff;
}
#center-fab {
    background-color: var(--main-blue);
    color: #fff;
    display: none;
}

#center-fab:active {
    background-color: var(--main-blue);
}

/* When shown, make sure it's displayed as flex */
#center-fab[style*="display: flex"] {
    display: flex !important;
}
#debug-fab {
    background-color: var(--bright-yellow);
    color: #fff;
}

#battle-fab {
    background-color: var(--green);
    color: #fff;
    grid-column: 1; /* First column */
    grid-row: 1; /* First and only row */
}

.icon-svg {
    fill: var(--white, #fff) !important;
    width: 32px;
    height: 32px;
    display: inline-block;
    vertical-align: middle;
    pointer-events: none;
    user-select: none;
    object-fit: contain;
}

/* Debug Grid Label Styles */
.debug-grid-label {
    width: 100px;
    height: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
    pointer-events: none;
    background: none;
    border: none;
    box-shadow: none;
  }

  .debug-grid-label-text {
    color: rgba(33,150,243,0.7);
    font-size: 10px;
    font-weight: bold;
    text-shadow: 0 1px 6px #fff;
    text-align: center;
    width: 100%;
    display: inline-block;
  }

  /* Rarity styles for debug grid */
  .debug-grid-rarity {
    font-size: 9px;
    font-weight: bold;
    padding: 1px 3px;
    border-radius: 3px;
    margin-top: 2px;
    display: inline-block;
  }

  .debug-grid-rarity.common {
    background-color: rgba(158, 158, 158, 0.7);
    color: white;
  }

  .debug-grid-rarity.starter {
    background-color: rgba(76, 175, 80, 0.7);
    color: white;
  }

  .debug-grid-rarity.scarce {
    background-color: rgba(33, 150, 243, 0.7);
    color: white;
  }

  .debug-grid-rarity.rare {
    background-color: rgba(156, 39, 176, 0.7);
    color: white;
  }

  .debug-grid-rarity.legendary {
    background-color: rgba(255, 152, 0, 0.7);
    color: white;
  }

  .debug-grid-rarity.mythical {
    background-color: rgba(244, 67, 54, 0.7);
    color: white;
  }

/* Trainer Popup Styles */
.trainer-popup {
  font-family: 'Poppins', Arial, sans-serif;
  max-width: 280px;
  padding: 12px;
}

.trainer-popup h3 {
  margin: 0 0 8px 0;
  color: var(--dark-grey);
  font-size: 1.2em;
  font-weight: 600;
}

.trainer-popup p {
  margin: 4px 0;
  color: var(--dark-grey);
  font-size: 0.9em;
}

.trainer-team {
  margin-top: 12px;
}

.trainer-team h4 {
  margin: 0 0 6px 0;
  color: var(--dark-grey);
  font-size: 1em;
  font-weight: 500;
}

.trainer-team-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.trainer-pokemon {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  background-color: var(--light-grey);
  border-radius: 6px;
  font-size: 0.85em;
}

.pokemon-name {
  font-weight: 500;
  color: var(--dark-grey);
}

.pokemon-level {
  font-size: 0.8em;
  color: var(--medium-grey);
  font-weight: 400;
}

.challenge-btn {
  width: 100%;
  margin-top: 12px;
  padding: 8px 16px;
  background-color: var(--pinky-red);
  color: white;
  border: none;
  border-radius: 6px;
  font-family: 'Poppins', Arial, sans-serif;
  font-size: 0.9em;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  tap-highlight-color: transparent;
}

.challenge-btn:hover {
  background-color: var(--dark-red);
}

.challenge-btn:active {
  background-color: var(--dark-red);
  transform: translateY(1px);
}

.challenge-button {
  width: 100%;
  margin-top: 12px;
  padding: 8px 16px;
  background-color: var(--pinky-red);
  color: white;
  border: none;
  border-radius: 6px;
  font-family: 'Poppins', Arial, sans-serif;
  font-size: 0.9em;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  tap-highlight-color: transparent;
}

.challenge-button:hover:not(:disabled) {
  background-color: var(--dark-red);
}

.challenge-button:active:not(:disabled) {
  background-color: var(--dark-red);
  transform: translateY(1px);
}

.challenge-button:disabled {
  background-color: var(--medium-grey);
  color: var(--light-grey);
  cursor: not-allowed;
  opacity: 0.6;
}

.team-requirement-warning,
.distance-warning {
  margin-top: 8px;
  padding: 6px 8px;
  background-color: rgba(255, 152, 0, 0.1);
  border: 1px solid rgba(255, 152, 0, 0.3);
  border-radius: 4px;
  color: #e65100;
  font-size: 0.8em;
  font-weight: 500;
  text-align: center;
}

.distance-warning {
  background-color: rgba(244, 67, 54, 0.1);
  border-color: rgba(244, 67, 54, 0.3);
  color: #c62828;
}